from dotenv import load_dotenv
load_dotenv()

# get the project rootpath for other filepath creation
import sys
from pathlib import Path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.services.validate_research_request import validate_research_request
from src.services.search_openalex_database import execute_openalex_search
from src.services.extract_epidemiology_data import extract_epidemiology_data_from_research_article

from src.services.redis_queues import validate_request_queue, openalex_queue, screening_queue, extraction_queue

    