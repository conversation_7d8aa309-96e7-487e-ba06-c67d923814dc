from dotenv import load_dotenv
load_dotenv()

# Add project root to Python path for imports
import sys
from pathlib import Path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import json
import requests
from typing import List, Optional, Union, Tuple
import re
import html
from typing import Dict
import os
from bs4 import BeautifulSoup, Tag
from bs4.element import NavigableString
from requests.exceptions import <PERSON><PERSON><PERSON><PERSON>rror, ConnectionError, RequestException, Timeout
from concurrent.futures import ThreadPoolExecutor, as_completed, Future


class PubMedToMarkdownConverter:
    """Converts PubMed/PMC HTML articles to markdown format."""

    def __init__(self, add_metadata: bool = False, add_scanned_document: bool = False, remove_irrelevant_sections: bool = True):
        self.add_metadata = add_metadata
        self.add_scanned_document = add_scanned_document
        self.base_url = "https://pmc.ncbi.nlm.nih.gov"
 
        if remove_irrelevant_sections:
            self.ignore_headers = json.load(open(
                os.path.join(project_root, "data", "ignore_article_headers.json"))
            )
        else:
            self.ignore_headers = []
    
    def convert_pmcid_to_markdown(self, pmcid: str) -> Tuple[Dict[str, str], str]:
        """Takes a PMCID and returns the markdown content of the article."""
        html_content = self.get_html_from_pmcid(pmcid)
        metadata, markdown_content = self.convert_html(html_content)
        return metadata, markdown_content
    
    def get_html_from_pmcid(self, pmcid: str) -> str:
        """
        Fetches the full article text based on PMCID from the NCBI website and returns the HTML text of the article in string format from the url: https://www.ncbi.nlm.nih.gov/pmc/articles/{pmcid}/?report=classic

        Args:
            pmcid (str): The PMCID to fetch

        Returns:
            str: The article html text if successful, empty string if there was an error
        """
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
        }
        url = f"https://www.ncbi.nlm.nih.gov/pmc/articles/{pmcid}/?report=classic"
        try:
            response = requests.get(url, headers=headers)
            response.raise_for_status()  # This will raise an exception for 4XX/5XX status codes
            return response.text
        except requests.exceptions.HTTPError as e:
            raise HTTPError(f"HTTP error occurred for PMCID {pmcid}: {str(e)}")
        except requests.exceptions.ConnectionError as e:
            raise ConnectionError(f"Connection error occurred for PMCID {pmcid}: {str(e)}")
        except requests.exceptions.Timeout as e:
            raise Timeout(f"Request timed out for PMCID {pmcid}: {str(e)}")
        except requests.exceptions.RequestException as e:
            raise RequestException(f"An error occurred while fetching PMCID {pmcid}: {str(e)}")

    def convert_html(self, html_content: str) -> Tuple[Dict[str, str], str]:
        """
        Convert HTML content to markdown string

        Args:
            html_content (str): The HTML content to convert

        Returns:
            Tuple[Dict[str, str], str]: A tuple with the metadata and the markdown content
        """
        add_metadata = bool(self.add_metadata)
        add_scanned_document = bool(self.add_scanned_document)
        ignore_headers = list(self.ignore_headers)
        soup = BeautifulSoup(html_content, "html.parser")

        # Build markdown document
        markdown_parts = []

        # Extract and format metadata
        metadata = self._extract_metadata(soup)
        markdown_parts.append(self._format_metadata(metadata, add_metadata))

        # Check if this is a scanned document
        is_scanned = self._is_scanned_document(soup)

        if is_scanned:
            markdown_parts.append(self._handle_scanned_document(soup, add_scanned_document))
        else:
            # Extract main content sections
            markdown_parts.append(self._extract_abstract(soup))
            markdown_parts.append(self._extract_main_content(soup, ignore_headers))
            markdown_parts.append(self._extract_references(soup, ignore_headers))

        # Join all parts and clean up
        markdown = "\n\n".join(filter(None, markdown_parts))
        return metadata, self._clean_markdown(markdown)

    def _extract_pmcid(self, soup) -> str:
        """Extract PMCID from the HTML"""
        # Look for PMCID in canonical URL or meta tags
        canonical = soup.find("link", {"rel": "canonical"})
        if canonical and canonical.get("href"):
            match = re.search(r"PMC(\d+)", canonical["href"])
            if match:
                return f"PMC{match.group(1)}"

        # Look in text content
        pmcid_text = soup.find(text=re.compile(r"PMCID:\s*PMC\d+"))
        if pmcid_text:
            match = re.search(r"PMC\d+", pmcid_text)
            if match:
                return match.group(0)

        return ""

    def _extract_metadata(self, soup) -> Dict[str, str]:
        """Extract article metadata from HTML head."""
        metadata = {}

        # Extract citation metadata
        meta_mappings = {
            "title": "citation_title",
            "journal": "citation_journal_title",
            "doi": "citation_doi",
            "pmid": "citation_pmid",
            "pdf_url": "citation_pdf_url",
            "publication_date": "citation_publication_date",
            "abstract_url": "citation_abstract_html_url",
            "fulltext_url": "citation_fulltext_html_url",
        }

        for key, meta_name in meta_mappings.items():
            meta_tag = soup.find("meta", attrs={"name": meta_name})
            if meta_tag and meta_tag.get("content"):
                metadata[key] = meta_tag["content"].strip()

        # Extract authors
        authors = []
        author_tags = soup.find_all("meta", attrs={"name": "citation_author"})
        for tag in author_tags:
            if tag.get("content"):
                authors.append(tag["content"].strip())
        metadata["authors"] = authors

        # Extract title from page title if not found in meta
        if "title" not in metadata:
            title_tag = soup.find("title")
            if title_tag:
                title = title_tag.get_text().strip()
                # Remove " - PMC" suffix if present
                title = re.sub(r"\s*-\s*PMC\s*$", "", title)
                metadata["title"] = title

        metadata["pmcid"] = self._extract_pmcid(soup)

        return metadata

    def _format_metadata(self, metadata: Dict[str, str], add_metadata: bool) -> str:
        """Format metadata as markdown header."""
        lines = []

        # Title
        if "title" in metadata:
            lines.append(f"# {metadata['title']}")
            lines.append("")

        if add_metadata:
            lines.append("## Metadata")

            # Authors
            if "authors" in metadata and metadata["authors"]:
                authors_str = ", ".join(metadata["authors"])
                lines.append(f"**Authors: <AUTHORS>

            # Journal
            if "journal" in metadata:
                lines.append(f"**Journal:** {metadata['journal']}")

            # Publication date
            if "publication_date" in metadata:
                lines.append(f"**Date:** {metadata['publication_date']}")

            # DOI
            if "doi" in metadata:
                lines.append(
                    f"**DOI:** [{metadata['doi']}](https://doi.org/{metadata['doi']})"
                )

            # PMID
            if "pmid" in metadata:
                lines.append(f"**PMID:** {metadata['pmid']}")

            # PMCID
            if "pmcid" in metadata:
                lines.append(f"**PMCID:** {metadata['pmcid']}")

            # URL
            if "pmcid" in metadata:
                url = f"https://www.ncbi.nlm.nih.gov/pmc/articles/{metadata['pmcid']}/"
                lines.append(f"**URL:** {url}")

            # PDF
            if "pdf_url" in metadata:
                lines.append(f"**PDF:** [{metadata['pdf_url']}]({metadata['pdf_url']})")

        return "\n".join(lines)

    def _is_scanned_document(self, soup) -> bool:
        """Check if this is a scanned document (legacy format)."""
        scanned_indicators = [
            soup.find("section", class_="scanned-pages"),
            soup.find("meta", attrs={"name": "ncbi_type", "content": "scanpage"}),
            soup.find("figure", class_="fig-scanned"),
        ]
        return any(indicator is not None for indicator in scanned_indicators)

    def _handle_scanned_document(self, soup, add_scanned_document: bool) -> str:
        """Handle scanned documents with limited structured content."""
        if not add_scanned_document:
            raise ValueError("Identified a scanned document but add_scanned_document is False")

        lines = []
        lines.append(
            "*Note: This is a scanned document with limited structured text. Full content available in PDF.*"
        )
        lines.append("")

        # Try to extract abstract if available
        abstract = self._extract_abstract(soup)
        if abstract.strip():
            lines.append(abstract)

        # Add scanned page images
        scanned_section = soup.find("section", class_="scanned-pages")
        if scanned_section:
            lines.append("## Full Text (Scanned Pages)")
            lines.append("")

            figures = scanned_section.find_all("figure", class_="fig-scanned")
            for i, figure in enumerate(figures, 1):
                img = figure.find("img")
                if img and img.get("src"):
                    alt_text = img.get("alt", f"Page {i}")
                    lines.append(f"### Page {i}")
                    lines.append(f"![{alt_text}]({img['src']})")
                lines.append("")

        return "\n".join(lines)

    def _extract_abstract(self, soup) -> str:
        """Extract abstract section."""
        abstract_section = soup.find("section", class_="abstract")
        if not abstract_section:
            return ""

        lines = ["## Abstract", ""]

        # Handle structured abstracts with subsections
        subsections = abstract_section.find_all(["h3", "h4"], class_="pmc_sec_title")
        if subsections:
            current_section = None
            for element in abstract_section.descendants:
                if isinstance(element, Tag):
                    element_class = element.get("class")
                    if isinstance(element_class, list):
                        element_class = element_class
                    else:
                        element_class = []
                        
                    if element.name in ["h3", "h4"] and "pmc_sec_title" in element_class:
                        current_section = element.get_text().strip()
                        # Remove trailing colon if present to avoid double colons
                        current_section = current_section.rstrip(":")
                        lines.append(f"**{current_section}:** ")
                    elif element.name == "p" and current_section:
                        text = self._clean_text(element.get_text())
                        if text:
                            lines.append(text)
                            lines.append("")
        else:
            # Simple abstract without subsections
            paragraphs = abstract_section.find_all("p")
            for p in paragraphs:
                text = self._clean_text(p.get_text())
                if text:
                    lines.append(text)
                    lines.append("")

        return "\n".join(lines)

    def _extract_main_content(self, soup, ignore_headers: List[str]) -> str:
        """Extract main article content sections."""
        main_body = soup.find("section", class_="main-article-body")
        if not main_body:
            return ""

        lines = []

        # Find all major sections
        sections = main_body.find_all("section", id=True)

        for section in sections:
            # Skip abstract (already handled), references (handled separately), and keywords (part of abstract)
            if any(
                cls in section.get("class", [])
                for cls in ["abstract", "ref-list", "kwd-group"]
            ):
                continue

            section_content = self._process_section(section, ignore_headers)
            if section_content:
                lines.append(section_content)

        return "\n".join(lines)

    def _process_section(self, section: Tag, ignore_headers: List[str]) -> str:
        """Process a single content section."""
        lines = []

        # Extract section title
        title_tag = section.find(["h1", "h2", "h3", "h4"], class_="pmc_sec_title")
        if title_tag:
            title = self._clean_text(title_tag.get_text())
            title_tag_name = getattr(title_tag, "name")
            if isinstance(title_tag_name, str):
                level = int(title_tag_name[1])  # h2 -> 2, h3 -> 3, etc.
            
            # skip section if it is present in ignore_headers
            if any(title.strip().lower().startswith(header) for header in ignore_headers):
                return ""

            markdown_level = "#" * level
            lines.append(f"{markdown_level} {title}")
            lines.append("")

        # Process section content
        for element in section.children:
            if isinstance(element, Tag):
                if element.name == "p":
                    text = self._process_paragraph(element)
                    if text:
                        lines.append(text)
                        lines.append("")
                elif element.name == "section" and element.get("class"):
                    element_class = element.get("class")
                    if isinstance(element_class, list):
                        element_class = element_class
                    else:
                        element_class = []

                    # Handle subsections, tables, figures
                    if "tw" in list(element_class):  # Table
                        table_md = self._process_table(element)
                        if table_md:
                            lines.append(table_md)
                    elif any(
                        cls in element_class for cls in ["fig", "figure"]
                    ):  # Figure
                        figure_md = self._process_figure(element)
                        if figure_md:
                            lines.append(figure_md)
                    else:  # Subsection
                        subsection_md = self._process_section(element, ignore_headers)
                        if subsection_md:
                            lines.append(subsection_md)
                elif element.name == "figure":
                    figure_md = self._process_figure(element)
                    if figure_md:
                        lines.append(figure_md)
                elif element.name == "table":
                    # Handle direct table elements
                    table_md = self._convert_table_to_markdown(element)
                    if table_md:
                        lines.append(table_md)

        return "\n".join(lines)

    def _process_paragraph(self, p_tag: Tag) -> str:
        """Process paragraph with inline formatting and citations."""
        text = ""

        def process_element(element):
            """Process a single element and return its markdown representation."""
            if isinstance(element, NavigableString):
                return str(element)
            elif isinstance(element, Tag):
                if element.name == "em" or element.name == "i":
                    return f"*{element.get_text()}*"
                elif element.name == "strong" or element.name == "b":
                    return f"**{element.get_text()}**"
                elif element.name == "sub":
                    return f"_{element.get_text()}_"
                elif element.name == "sup":
                    return f"^{element.get_text()}^"
                elif element.name == "a":
                    # Handle citations and cross-references
                    link_text = element.get_text().strip()
                    href = element.get("href", "")
                    if isinstance(href, str):
                        pass
                    else:
                        href = str(href)

                    if href.startswith("#"):
                        # Internal reference
                        return f"[{link_text}]({href})"
                    elif href:
                        # External link
                        return f"[{link_text}]({href})"
                    else:
                        return link_text
                else:
                    # For any other tag, recursively process its contents
                    result = ""
                    for child in element.contents:
                        result += process_element(child)
                    return result
            return ""

        # Process all direct contents of the paragraph
        for element in p_tag.contents:
            text += process_element(element)

        return self._clean_text(text)

    def _process_table(self, table_section: Tag) -> str:
        """Process table section."""
        lines = []

        # Extract table title
        title_tag = table_section.find(["h3", "h4"], class_="obj_head")
        title = ""
        if title_tag:
            title = self._clean_text(title_tag.get_text())
            lines.append(f"### {title}")
            lines.append("")

        # Extract and convert table first
        table_tag = table_section.find("table")
        if table_tag:
            table_md = self._convert_table_to_markdown(table_tag)
            if table_md:
                lines.append(table_md)

        # Extract table caption and place below table
        # Look for caption in both 'caption' class and 'tw-foot' class
        caption_div = table_section.find("div", class_="caption")
        if not caption_div:
            caption_div = table_section.find("div", class_="tw-foot")

        if caption_div:
            caption = self._clean_text(caption_div.get_text())
            # Extract table number from title if available
            table_number = ""
            if title:
                # Try to extract table number from title like "Table 1." or "Table 1:"
                match = re.search(r"Table\s+(\d+)", title, re.IGNORECASE)
                if match:
                    table_number = match.group(1)

            # Add blank line before caption to prevent merging with table
            lines.append("")
            if table_number:
                lines.append(f"Table {table_number} Caption: {caption}")
            else:
                lines.append(f"Table Caption: {caption}")
            lines.append("")

        return "\n".join(lines)

    def _convert_table_to_markdown(self, table_tag: Tag) -> str:
        """Convert HTML table to markdown table."""
        rows = []
        max_cols = 0

        # Process header
        thead = table_tag.find("thead")
        header_rows = []
        if thead:
            header_rows = thead.find_all("tr")
            for row in header_rows:
                cells = row.find_all(["th", "td"])
                row_data = []
                for cell in cells:
                    # Handle colspan
                    colspan = int(cell.get("colspan", 1))
                    text = self._clean_text(cell.get_text()).strip()
                    # Escape pipe characters and clean up text
                    text = text.replace("|", "\\|").replace("\n", " ")
                    # Handle empty cells
                    if not text:
                        text = " "
                    row_data.append(text)
                    # Add empty cells for colspan > 1
                    for _ in range(colspan - 1):
                        row_data.append("")
                rows.append(row_data)
                max_cols = max(max_cols, len(row_data))

        # Process body
        tbody = table_tag.find("tbody")
        if tbody:
            body_rows = tbody.find_all("tr")
        else:
            # No explicit tbody, get all tr elements
            body_rows = table_tag.find_all("tr")
            if header_rows:
                # Remove header rows if we already processed them
                body_rows = body_rows[len(header_rows) :]

        for row in body_rows:
            cells = row.find_all(["td", "th"])
            row_data = []
            for cell in cells:
                # Handle colspan (markdown doesn't support rowspan)
                colspan = int(cell.get("colspan", 1))
                text = self._clean_text(cell.get_text()).strip()
                # Escape pipe characters and clean up text
                text = text.replace("|", "\\|").replace("\n", " ")
                # Handle empty cells
                if not text:
                    text = " "
                row_data.append(text)
                # Add empty cells for colspan > 1
                for _ in range(colspan - 1):
                    row_data.append("")
            rows.append(row_data)
            max_cols = max(max_cols, len(row_data))

        if not rows:
            return ""

        # Normalize all rows to have same number of columns
        for row in rows:
            while len(row) < max_cols:
                row.append("")

        # Convert to markdown
        lines = []

        if rows:
            # Determine if we have proper headers
            has_proper_header = thead and header_rows

            if has_proper_header:
                header = rows[0]
                data_rows = rows[1:]
            else:
                # For tables without proper headers, use first row if it looks like headers
                # Otherwise create generic headers
                first_row = rows[0]
                # Check if first row looks like headers (contains non-numeric text)
                if any(
                    not cell.replace(".", "").replace("-", "").isdigit()
                    and cell.strip()
                    for cell in first_row
                ):
                    header = first_row
                    data_rows = rows[1:]
                else:
                    # Create descriptive headers based on content patterns
                    header = []
                    for i in range(max_cols):
                        if i == 0:
                            header.append("Category")
                        else:
                            header.append(f"Value {i}")
                    data_rows = rows

            # Header row
            lines.append("| " + " | ".join(header) + " |")

            # Separator row - use consistent width for better formatting
            separator_cols = ["---"] * len(header)
            lines.append("| " + " | ".join(separator_cols) + " |")

            # Data rows
            for row in data_rows:
                # Ensure row has same length as header
                while len(row) < len(header):
                    row.append("")
                lines.append("| " + " | ".join(row[: len(header)]) + " |")

        return "\n".join(lines)

    def _process_figure(self, figure_tag: Tag) -> str:
        """Process figure element."""
        lines = []

        # Extract figure title
        title_tag = figure_tag.find(["h3", "h4"], class_="obj_head")
        if title_tag:
            title = self._clean_text(title_tag.get_text())
            lines.append(f"### {title}")
            lines.append("")

        # Extract image
        img_tag = figure_tag.find("img")
        img_tag_src = getattr(img_tag, "src")
        if img_tag and img_tag_src:
            src = img_tag_src
            alt = getattr(img_tag, "alt", "Figure")

            # Ensure absolute URL
            if src.startswith("//"):
                src = "https:" + src
            elif src.startswith("/"):
                src = self.base_url + src

            lines.append(f"![{alt}]({src})")
            lines.append("")

            # Add zoom link if available
            zoom_link = figure_tag.find("a", class_="tileshop")
            zoom_link_href = getattr(zoom_link, "href")
            if zoom_link and zoom_link_href:
                lines.append(f"[View larger image]({zoom_link_href})")
                lines.append("")

        # Extract figure caption
        caption_tag = figure_tag.find("figcaption")
        if caption_tag:
            caption = self._clean_text(caption_tag.get_text())
            lines.append(caption)
            lines.append("")

        return "\n".join(lines)

    def _extract_references(self, soup, ignore_headers: List[str]) -> str:
        """Extract references section."""
        if any(header.startswith("references") for header in ignore_headers):
            return ""

        ref_section = soup.find("section", class_="ref-list")
        if not ref_section:
            return ""

        lines = ["## References", ""]

        # Find reference list
        ref_list = ref_section.find(["ul", "ol"], class_="ref-list")
        if ref_list:
            for i, li in enumerate(ref_list.find_all("li"), 1):
                ref_text = self._process_reference(li, i)
                if ref_text:
                    lines.append(ref_text)
                    lines.append("")

        return "\n".join(lines)

    def _process_reference(self, ref_item: Tag, ref_num: int) -> str:
        """Process individual reference."""
        parts = []

        # Add reference number
        parts.append(f"{ref_num}.")

        # Extract citation text
        cite_tag = ref_item.find("cite")
        if cite_tag:
            cite_text = self._clean_text(cite_tag.get_text())
            parts.append(cite_text)
        else:
            # No cite tag, use all text except links
            text_parts = []
            for element in ref_item.children:
                if isinstance(element, NavigableString):
                    text_parts.append(str(element))
                elif isinstance(element, Tag) and element.name != "a":
                    text_parts.append(element.get_text())
            cite_text = self._clean_text("".join(text_parts))
            parts.append(cite_text)

        # Extract links (DOI, PMC, PubMed)
        links = ref_item.find_all("a", href=True)
        link_parts = []
        for link in links:
            href = getattr(link, "href")
            if isinstance(href, str):
                pass
            else:
                href = str(href)
            text = link.get_text().strip()

            if "doi.org" in href or text.upper() == "DOI":
                link_parts.append(f"[DOI]({href})")
            elif "pmc.ncbi.nlm.nih.gov" in href or text.upper() == "PMC":
                link_parts.append(f"[PMC]({href})")
            elif "pubmed.ncbi.nlm.nih.gov" in href or text.upper() == "PUBMED":
                link_parts.append(f"[PubMed]({href})")
            else:
                link_parts.append(f"[{text}]({href})")

        if link_parts:
            parts.append(" " + " | ".join(link_parts))

        return " ".join(parts)

    def _clean_text(self, text: str) -> str:
        """Clean and normalize text."""
        if not text:
            return ""

        # Decode HTML entities
        text = html.unescape(text)

        # Normalize whitespace
        text = re.sub(r"\s+", " ", text)

        # Remove extra whitespace
        text = text.strip()

        return text

    def _clean_markdown(self, markdown: str) -> str:
        """Clean up final markdown output."""
        # Remove excessive blank lines
        markdown = re.sub(r"\n{3,}", "\n\n", markdown)

        # Ensure document ends with single newline
        markdown = markdown.strip() + "\n"

        return markdown


from typing import Generator

def download_fulltext_articles_generator(
    pmc_links: List[str], 
    add_metadata: bool = False,
    add_scanned_document: bool = False,
    remove_irrelevant_sections: bool = True,
    max_workers: int = 10
) -> Generator[Dict[str, str], None, None]:
    """
    Takes a list of PMCIDs and returns the markdown contents of the articles.

    Args:
        pmc_links (List[str]): The list of PubMed Central website links
        add_metadata (bool): Whether to add metadata to the markdown content
        add_scanned_document (bool): Whether to add scanned document to the markdown content
        remove_irrelevant_sections (bool): Whether to remove irrelevant sections e.g. ["references"]
        max_workers (int): The maximum number of workers to use

    Returns:
        Generator[Dict[str, str], None, None]: A generator of dictionaries with the status code, metadata, and markdown content
    """

    pmc_ids = ["PMC" + pmcid.split("/")[-1] for pmcid in pmc_links]
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # initialize the downloader with the given parameters and submit the tasks
        downloader = PubMedToMarkdownConverter(
            add_metadata=add_metadata, 
            add_scanned_document=add_scanned_document, remove_irrelevant_sections=remove_irrelevant_sections
        )
        future_to_pmcid: Dict[Future, str] = {
            executor.submit(downloader.convert_pmcid_to_markdown, pmcid): pmcid_link 
            for pmcid, pmcid_link in zip(pmc_ids, pmc_links)
        }

        for future in as_completed(future_to_pmcid):
            pmcid_link = future_to_pmcid[future]
            response = {"status_code": "", "pmcid": pmcid_link}
            try:
                _, markdown_content = future.result()
                response["status_code"] = "SUCCESS"
                if len(markdown_content) < 250: # filter out the short articles with less than 250 characters
                    response["status_code"] = "FAILED"
                    response["error_message"] = "The fulltext markdown is too short"
                else:
                    response["fulltext_markdown"] = markdown_content
                yield response
            except Exception as e:
                response["status_code"] = "FAILED"
                response["pmcid"] = pmcid_link
                response["error_message"] = f"Failed to process PMCID {pmcid_link}: {e}"
                yield response

if __name__ == "__main__":
    pmc_links = ["https://www.ncbi.nlm.nih.gov/pmc/articles/3576554"]
    for response in download_fulltext_articles_generator(pmc_links):
        print(response)