class DiseaseValidationConfig:
    """
    Configuration for the Disease Validation Agent.
    """
    model_id = "gpt-4.1-mini"
    temperature = 0.0
    api_retries = 2
    use_reasoning = False
    debug_agent = True


class AbstractScreeningConfig:
    """
    Configuration for the Disease Abstract Screening Agent.
    """
    model_id = "gpt-4.1-mini"
    temperature = 0.0
    api_retries = 2
    use_reasoning = False
    debug_agent = True

class PrevalenceExtractionConfig:
    """
    Configuration for the Prevalence Extraction Agent.
    """
    model_id = "gpt-5"
    temperature = 1.0
    api_retries = 2
    use_reasoning = False
    debug_agent = True

class IncidenceExtractionConfig:
    """
    Configuration for the Incidence Extraction Agent.
    """
    model_id = "gpt-4.1"
    temperature = 0.0
    api_retries = 2
    use_reasoning = True
    debug_agent = True