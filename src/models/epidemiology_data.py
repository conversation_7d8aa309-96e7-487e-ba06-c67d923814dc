from typing import Optional, List, Dict
import pycountry
from pydantic import BaseModel, Field, field_validator

# global variables
continents = [
    'Asia', 'Europe', 'Africa', 'North America', 'South America', 'Australia', 'Antarctica']
# regions = [
#     'Eastern Europe', 'Western Europe', 'Northern Europe', 'Southern Europe', 'Central Europe', 'Eastern Asia', 'Western Asia', 'Northern Asia', 'Southern Asia', 'Central Asia', 'Eastern Africa', 'Western Africa', 'Northern Africa', 'Southern Africa', 'Central Africa', 'Eastern North America', 'Western North America', 'Northern North America', 'Southern North America', 'Central North America', 'Eastern South America', 'Western South America', 'Northern South America', 'Southern South America', 'Central South America', 'Eastern Australia', 'Western Australia', 'Northern Australia', 'Southern Australia', 'Central Australia'
# ]
countries = [getattr(country, 'name') for country in pycountry.countries]

# Abstract Screening
class AbstractScreeningResult(BaseModel):
    """
    Pydantic model to capture the result of screening a research article abstract for a given disease.
    """
    is_epidemiology_focus: bool = Field(
        ...,
        description=("Whether the main focus of the article is on epidemiology research of the given disease name."),
    )
    reports_prevalence: bool = Field(
        ...,
        description=("Whether the article reports prevalence data for the given disease name."),
    )
    reports_incidence: bool = Field(
        ...,
        description=("Whether the article reports incidence data for the given disease name."),
    )

# Data Extraction
class DataExtractionInput(BaseModel):
    """
    Pydantic model to capture the input for data extraction from research articles about given disease or condition.
    """
    pmc_link: str = Field(
        ..., description="The PMC link of the research article."
    )
    disease_name: str = Field(
        ..., description="The name of the disease or condition."
    )
    is_epidemiology_focus: bool = Field(
        default=False, description="Whether the main focus of the article is on epidemiology research of the given disease name."
    )
    reports_prevalence: bool = Field(   
        default=False, description="Whether the article reports prevalence data for the given disease name."
    )
    reports_incidence: bool = Field(
        default=False, description="Whether the article reports incidence data for the given disease name."
    )
    screening_status: str = Field(
        default="NOT_SCREENED", description="The status of the screening of the article."
    )
    screening_message: str = Field(
        default="UNDEFINED", description="The message of the screening of the article."
    )

# Prevalence Record
class PrevalenceRecord(BaseModel):
    """
    Pydantic model to capture prevalence datapoints from research articles about given disease or condition.
    """
    prevalence_percent: float = Field(
        ..., ge=0, le=100, description="Prevalence as percent (0-100)."
    )
    geography: str = Field(
        ..., description="Geography of the reported prevalence data. This should be either Country name (e.g., India, United States of America), or Continent name (e.g., Asia, Europe, Africa)."
    )
    year_range: str = Field(
        ..., description="Year for point prevalence or year range for period prevalence (e.g., '2020' or '2015-2020')."
    )
    sex: str = Field(
        ..., description="Sex of reported prevalence data. This should be either 'Male', 'Female', or 'Both'. If reported data does not specify sex, it is assumed to be 'Both'."
    )

    @field_validator('geography')
    def validate_geography(cls, v):
        # check if v is in the list of countries, regions, or continents
        if v not in countries + continents:
            raise ValueError(f"Invalid geography={v}. Geography must be either known Country, or Continent.")
        return v
    
    @field_validator('year_range')
    def validate_year_range(cls, v):
        # check first if its year or year range
        if v.count('-') == 0:
            if v not in [str(year) for year in range(1900, 2025)]:
                raise ValueError(f"Invalid year={v}. Year must be between 1900 and 2025.")
            return v
        if v.count('-') == 1:
            year1, year2 = v.split('-')

            if year1 not in [str(year) for year in range(1900, 2025)]:
                raise ValueError(f"Invalid year={year1}. Year must be between 1900 and 2025.")
            if year2 not in [str(year) for year in range(1900, 2025)]:
                raise ValueError(f"Invalid year={year2}. Year must be between 1900 and 2025.")
            
            if int(year1) > int(year2):
                raise ValueError(f"Invalid year range={v}. Year range must be in ascending order.")
            return v
        return v
    
    @field_validator('sex')
    def validate_gender(cls, v):
        if v not in ['Male', 'Female', 'Both']:
            raise ValueError(f"Invalid sex={v}. Sex must be either 'Male', 'Female', or 'Both'.")
        return v
    

# Incidence Record
class IncidenceRecord(BaseModel):
    """
    Pydantic model to capture incidence datapoints from research articles about given disease or condition.
    """
    incidence_rate: float = Field(
        ..., ge=0, description="Incidence rate per 100,000 person-years."
    )
    geography: str = Field(
        ..., description="Geography of the reported incidence data. This should be either Country name (e.g., India, United States of America), or Continent name (e.g., Asia, Europe, Africa)."
    )
    year_range: str = Field(
        ..., description="Year for incidence rate or year range for cumulative incidence (e.g., '2020' or '2015-2020')."
    )
    sex: str = Field(
        ..., description="Sex of reported incidence data. This should be either 'Male', 'Female', or 'Both'. If reported data does not specify sex, it is assumed to be 'Both'."
    )

    @field_validator('geography')
    def validate_geography(cls, v):
        # check if v is in the list of countries, regions, or continents
        if v not in countries + continents:
            raise ValueError(f"Invalid geography={v}. Geography must be either known Country, or Continent.")
        return v
    
    @field_validator('year_range')
    def validate_year_range(cls, v):
        # check first if its year or year range
        if v.count('-') == 0:
            if v not in [str(year) for year in range(1900, 2025)]:
                raise ValueError(f"Invalid year={v}. Year must be between 1900 and 2025.")
            return v
        if v.count('-') == 1:
            year1, year2 = v.split('-')
            # check if year1 and year2 are integers
            if not year1.isdigit() or not year2.isdigit():
                raise ValueError(f"Invalid year range={v}. Year range must be in the format 'YYYY-YYYY'.")
            
            # check if year1 and year2 are in ascending order
            if int(year1) > int(year2):
                raise ValueError(f"Invalid year range={v}. Year range must be in ascending order.")

            # check if year1 and year2 are in the range 1900-2025
            if year1 not in [str(year) for year in range(1900, 2025)]:
                raise ValueError(f"Invalid year={year1}. Year must be between 1900 and 2025.")
            if year2 not in [str(year) for year in range(1900, 2025)]:
                raise ValueError(f"Invalid year={year2}. Year must be between 1900 and 2025.")
            
        return v
    
    @field_validator('sex')
    def validate_sex(cls, v):
        if v not in ['Male', 'Female', 'Both']:
            raise ValueError(f"Invalid sex={v}. Sex must be either 'Male', 'Female', or 'Both'.")
        return v


# Wrapper models for LLM output schemas
from typing import Any
from pydantic import model_validator

class PrevalenceDataList(BaseModel):
    """Wrapper model for list of prevalence records."""
    data: List[PrevalenceRecord] = Field(
        default=[], description="List of prevalence data points"
    )

    @model_validator(mode="after")
    def check_no_duplicate_prevalence_records(self) -> "PrevalenceDataList":
        """
        Validates that there are no duplicate PrevalenceRecord entries in the data list.
        """
        seen = set()
        for record in self.data:
            # Use tuple of sorted items for hashable comparison
            record_tuple = tuple(sorted(record.model_dump().items()))
            if record_tuple in seen:
                raise ValueError(
                    "Duplicate PrevalenceRecord found in data list: "
                    f"{record.model_dump()}"
                )
            seen.add(record_tuple)
        return self


class IncidenceDataList(BaseModel):
    """Wrapper model for list of incidence records."""
    data: List[IncidenceRecord] = Field(
        default=[], description="List of incidence data points"
    )

    @model_validator(mode="after")
    def check_no_duplicate_incidence_records(self) -> "IncidenceDataList":
        """
        Validates that there are no duplicate IncidenceRecord entries in the data list.
        """
        seen = set()
        for record in self.data:
            record_tuple = tuple(sorted(record.model_dump().items()))
            if record_tuple in seen:
                raise ValueError(
                    "Duplicate IncidenceRecord found in data list: "
                    f"{record.model_dump()}"
                )
            seen.add(record_tuple)
        return self

class DataExtractionOutput(BaseModel):
    """
    Pydantic model to capture the output for data extraction from research articles about given disease or condition.
    """
    pmc_link: str = Field(
        ..., description="The PMC link of the research article."
    )
    disease_name: str = Field(
        ..., description="The name of the disease or condition."
    )
    fulltext_available: bool = Field(
        default=False, description="Whether the fulltext of the article is available."
    )
    fulltext_path: str = Field(
        default="", description="The path to the fulltext of the article."
    )
    prevalence_extraction_status: str = Field(
        default="NOT_EXTRACTED", description="The status of the prevalence extraction."
    )
    prevalence_extraction_message: str = Field(
        default="UNDEFINED", description="The message of the prevalence extraction."
    )
    prevalence_data: List[PrevalenceRecord] = Field(default=[], description="List of prevalence data points.")
    incidence_extraction_status: str = Field(
        default="NOT_EXTRACTED", description="The status of the incidence extraction."
    )
    incidence_extraction_message: str = Field(
        default="UNDEFINED", description="The message of the incidence extraction."
    )
    incidence_data: List[IncidenceRecord] = Field(default=[], description="List of incidence data points.")


if __name__ == "__main__":
    # print how PrevalenceRecord model dump looks for an LLM
    print(PrevalenceRecord.model_json_schema())

    # print how IncidenceRecord model dump looks for an LLM
    print(IncidenceRecord.model_json_schema())