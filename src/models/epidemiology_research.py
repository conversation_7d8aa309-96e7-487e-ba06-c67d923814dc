from pydantic import BaseModel, Field
from typing import List, Self
from pydantic import model_validator

class DiseaseValidation(BaseModel):
    """
    Model for validating disease names.

    Attributes:
        is_disease (bool): Whether the disease name is valid.
        standardized_disease_name (str): The full name of the disease in camel-case format.
        explanation (str): The explanation for why the user term is considered as valid or invalid disease name.
        suggested_corrections (Optional[List[str]]): The suggested corrections for the disease name if it is invalid term.
            If is_disease is True, this must be None or an empty list.
    """

    is_disease: bool = Field(
        ..., description="Whether the disease name is valid"
    )
    standardized_disease_name: str = Field(
        ..., description="The full name of the disease in camel-case format"
    )
    standard_disease_synonyms: List[str] = Field(
        ..., description="The standard synonyms of the disease name which are used in the research literature"
    )
    explanation: str = Field(
        ..., description="The explanation for why the user term is considered as valid or invalid disease name"
    )
    suggested_corrections: List[str] = Field(
        [],
        description="The suggested corrections for the disease name if it is invalid term"
    )

    @model_validator(mode="after")
    def _validate_dependent_attributes(self) -> Self:
        # validate suggested corrections
        if self.is_disease and len(self.suggested_corrections)>0: 
            raise ValueError(
                "If `is_disease` is True, then `suggested_corrections` must be an empty list. Instead got {self.suggested_corrections}"
            )
        elif not self.is_disease and len(self.suggested_corrections)==0:
            raise ValueError(
                "If `is_disease` is False, then `suggested_corrections` must be a non-empty list but instead got an empty list"
            )
        
        # validate standard disease synonyms
        if not self.is_disease and len(self.standard_disease_synonyms)>0:
            raise ValueError(
                "If `is_disease` is False, then `standard_disease_synonyms` must be a empty list. Instead got {self.standard_disease_synonyms}"
            )
        return self


class ResearchRequest(BaseModel):
    disease_name: str = Field(..., description="The full name of the disease for epidemiology research")
    selected_country: str = Field(..., description="The name of the country about epidemiology research")
    min_published_date: str = Field(..., description="The minimum published date of the research articles")
    max_published_date: str = Field(..., description="The maximum published date of the research articles")
    topk_articles: int = Field(..., description="The number of top search results to return")

class OpenAlexSearchRequest(BaseModel):
    disease_names: List[str] = Field(..., description="List of disease name and its synonyms for epidemiology research")
    selected_country: str = Field(..., description="The name of the country about epidemiology research")
    min_published_date: str = Field(..., description="The minimum published date of the research articles")
    max_published_date: str = Field(..., description="The maximum published date of the research articles")
    topk_articles: int = Field(..., description="The number of top search results to priortize")
    cursor: str = Field(..., description="The cursor to start the search from")