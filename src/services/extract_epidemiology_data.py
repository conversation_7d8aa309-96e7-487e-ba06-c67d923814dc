from dotenv import load_dotenv
load_dotenv()

# get the project rootpath for other filepath creation
import sys
from pathlib import Path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import random, traceback
from textwrap import dedent
from typing import Dict, List, Generator, Optional, Any
from agno.agent import Agent
from agno.models.openai import OpenAIChat
import json
import os
from pydantic import BaseModel, Field
from agno.tools.reasoning import ReasoningTools
from concurrent.futures import ThreadPoolExecutor, as_completed
import yaml
from pathlib import Path
import pycountry

from src.models.epidemiology_data import DataExtractionInput, PrevalenceRecord, IncidenceRecord, DataExtractionOutput, PrevalenceDataList, IncidenceDataList
from src.helpers.config import PrevalenceExtractionConfig, IncidenceExtractionConfig
from src.helpers.pubmed_handler import download_fulltext_articles_generator
from src.helpers.general_utils import setup_logging
from agno.tools.calculator import CalculatorTools

LOG_LEVEL = os.getenv("LOG_LEVEL", "WARNING")
logger = setup_logging(logging_level=LOG_LEVEL)

# Config and prompts
project_root = Path(__file__).parent.parent.parent
with open(os.path.join(f"{project_root}/src/prompts/prevalence_extraction.yaml")) as f:
    prevalence_prompts = yaml.safe_load(f)

with open(os.path.join(f"{project_root}/src/prompts/incidence_extraction.yaml")) as f:
    incidence_prompts = yaml.safe_load(f)
os.makedirs(os.path.join(project_root, "data", "fulltext"), exist_ok=True)

prevalence_config = PrevalenceExtractionConfig()
incidence_config = IncidenceExtractionConfig()

def retrieve_standard_country_name(country_name: str) -> str:
    """
    Get the standardized name of country from the country name. If not found, return "Unknown".
    """
    country_object = pycountry.countries.search_fuzzy(country_name)[0]
    if country_object is None:
        return "Unknown"
    return getattr(country_object, "name", "Unknown")


def extract_prevalence_data_from_fulltext(
    pmc_link: str, disease: str, fulltext_markdown: str
) -> tuple[str, str, List[PrevalenceRecord]]:
    """
    Extract prevalence data points from a full-text research article for a specific disease.

    Args:
        pmc_link (str): The Pubmed central website link to the article.
        disease (str): The disease name to extract prevalence data for.
        fulltext_markdown (str): The full-text article content in markdown format.

    Returns:
        List[Dict]: List of dictionaries containing prevalence data points following PrevalenceRecord schema.
    """
    prevalence_agent = Agent(
        model=OpenAIChat(id=prevalence_config.model_id, temperature=prevalence_config.temperature),
        description=prevalence_prompts["description"],
        instructions=prevalence_prompts["instructions"],
        tools=[CalculatorTools(), retrieve_standard_country_name],
        reasoning=prevalence_config.use_reasoning,
        retries=prevalence_config.api_retries,
        exponential_backoff=True,
        output_schema=PrevalenceDataList,
        debug_mode=prevalence_config.debug_agent,
        telemetry=False
    )
    
    prompt = f"""
    Extract prevalence data points from the following research article:

    **DISEASE NAME:** {disease}

    **FULL-TEXT ARTICLE:**
    {fulltext_markdown}
    """

    try:
        response = prevalence_agent.run(prompt)
        if isinstance(response.content, PrevalenceDataList):
            return "SUCCESS", "" , response.content.data
        else:
            logger.warning(f"Unexpected response type for prevalence extraction from {pmc_link}: {type(response.content)}")
            return "FAILED", f"Unexpected response type for prevalence extraction from {pmc_link}: {type(response.content)}", []
    except Exception as e:
        logger.error(f"Failed to extract prevalence data from {pmc_link}: {traceback.format_exc()}")
        return "FAILED", f"Failed to extract prevalence data from {pmc_link}: {traceback.format_exc()}", []


def extract_incidence_data_from_fulltext(
    pmc_link: str, disease: str, fulltext_markdown: str
) -> tuple[str, str, List[IncidenceRecord]]:
    """
    Extract incidence data points from a full-text research article for a specific disease.

    Args:
        pmc_link (str): The Pubmed central website link to the article.
        disease (str): The disease name to extract incidence data for.
        fulltext_markdown (str): The full-text article content in markdown format.

    Returns:
        List[Dict]: List of dictionaries containing incidence data points following IncidenceRecord schema.
    """
    incidence_agent = Agent(
        model=OpenAIChat(id=incidence_config.model_id, temperature=incidence_config.temperature),
        description=incidence_prompts["description"],
        instructions=incidence_prompts["instructions"],
        tools=[CalculatorTools(), retrieve_standard_country_name],
        reasoning=incidence_config.use_reasoning,
        retries=incidence_config.api_retries,
        exponential_backoff=True,
        output_schema=IncidenceDataList,
        debug_mode=incidence_config.debug_agent,
        telemetry=False
    )
    
    prompt = f"""
    Extract incidence data points from the following research article:

    **DISEASE NAME:** {disease}

    **FULL-TEXT ARTICLE:**
    {fulltext_markdown}
    """

    try:
        response = incidence_agent.run(prompt)
        if isinstance(response.content, IncidenceDataList):
            return "SUCCESS", "" , response.content.data
        else:
            logger.warning(f"Unexpected response type for incidence extraction from {pmc_link}: {type(response.content)}")
            return "FAILED", f"Unexpected response type for incidence extraction from {pmc_link}: {type(response.content)}", []
    except Exception as e:
        logger.error(f"Failed to extract incidence data from {pmc_link}: {traceback.format_exc()}")
        return "FAILED", f"Failed to extract incidence data from {pmc_link}: {traceback.format_exc()}", []


def extract_epidemiology_data_from_research_article(data_extraction_input: DataExtractionInput) -> DataExtractionOutput:
    """
    Main function to extract epidemiology data from research articles based on screening results.
    """
    pmc_id = "PMC" + data_extraction_input.pmc_link.split("/")[-1]
    pmc_link = data_extraction_input.pmc_link
    pmc_fulltext_markdown = ""

    disease_name = data_extraction_input.disease_name
    reports_prevalence = data_extraction_input.reports_prevalence
    reports_incidence = data_extraction_input.reports_incidence
    screening_status = data_extraction_input.screening_status

    data_extraction_output = DataExtractionOutput(
        pmc_link=pmc_link,
        disease_name=disease_name,
        fulltext_available=False,
        prevalence_extraction_status="SKIPPED",
        prevalence_extraction_message="UNDEFINED",
        prevalence_data=[],
        incidence_extraction_status="NOT_EXTRACTED",
        incidence_extraction_message="UNDEFINED",
        incidence_data=[]
    )
    
    if screening_status != "SUCCESS" or (not reports_prevalence and not reports_incidence):
        data_extraction_output.prevalence_extraction_status = "SKIPPED"
        data_extraction_output.prevalence_extraction_message = "Skipped - no prevalence data reported in abstract"
        data_extraction_output.incidence_extraction_status = "SKIPPED"
        data_extraction_output.incidence_extraction_message = "Skipped - no incidence data reported in abstract"
    else:
        # Download full-text article
        for result in download_fulltext_articles_generator([pmc_link]):
            if result["status_code"] == "SUCCESS":
                data_extraction_output.fulltext_available = True
                data_extraction_output.fulltext_path = f"{project_root}/data/fulltext/{pmc_id}.md"
                with open(data_extraction_output.fulltext_path, "w") as f:
                    f.write(result["fulltext_markdown"])
                pmc_fulltext_markdown = result["fulltext_markdown"]
                break
            else:
                data_extraction_output.fulltext_available = False
                data_extraction_output.fulltext_path = ""
                data_extraction_output.prevalence_extraction_status = "SKIPPED"
                data_extraction_output.prevalence_extraction_message = "Unavailable fulltext"
                data_extraction_output.incidence_extraction_status = "SKIPPED"
                data_extraction_output.incidence_extraction_message = "Unavailable fulltext"
                break
        
        if data_extraction_output.fulltext_available:
            from concurrent.futures import ThreadPoolExecutor, as_completed, Future

            # Prepare tasks for parallel execution
            tasks = {}
            with ThreadPoolExecutor(max_workers=2) as executor:
                if reports_prevalence:
                    tasks["prevalence"] = executor.submit(
                        extract_prevalence_data_from_fulltext,
                        pmc_link,
                        disease_name,
                        pmc_fulltext_markdown,
                    )
                else:
                    data_extraction_output.prevalence_extraction_status = "SKIPPED"
                    data_extraction_output.prevalence_extraction_message = "Skipped - no prevalence data reported in abstract"
                if reports_incidence:
                    tasks["incidence"] = executor.submit(
                        extract_incidence_data_from_fulltext,
                        pmc_link,
                        disease_name,
                        pmc_fulltext_markdown,
                    )
                else:
                    data_extraction_output.incidence_extraction_status = "SKIPPED"
                    data_extraction_output.incidence_extraction_message = "Skipped - no incidence data reported in abstract"

                for key, future in tasks.items():
                    status, message, data = future.result()
                    if key == "prevalence":
                        data_extraction_output.prevalence_data = data
                        data_extraction_output.prevalence_extraction_status = status
                        data_extraction_output.prevalence_extraction_message = message
                    elif key == "incidence":
                        data_extraction_output.incidence_data = data
                        data_extraction_output.incidence_extraction_status = status
                        data_extraction_output.incidence_extraction_message = message
                    else:
                        logger.error(f"Unexpected key in thread pool executor: {key}")
    return data_extraction_output


if __name__ == "__main__":
    logger.debug("Testing Epidemiology Data Extraction...")
    data_extraction_input = DataExtractionInput(
        pmc_link="https://www.ncbi.nlm.nih.gov/pmc/articles/3576554",
        disease_name="Ulcerative Colitis",
        reports_prevalence=True,
        reports_incidence=False,
        screening_status="SUCCESS",
        screening_message=""
    )
    
    result = extract_epidemiology_data_from_research_article(data_extraction_input)
    logger.debug("Test Response of Epidemiology Data Extraction:")
    logger.debug(result.model_dump_json(indent=4))
