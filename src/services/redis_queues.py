from dotenv import load_dotenv
load_dotenv()
# get the project rootpath for other filepath creation
import sys
from pathlib import Path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import os
from redis import Redis
from rq import Queue

redis = Redis(
    host=os.getenv("REDIS_HOST", "localhost"),
    port=int(os.getenv("REDIS_PORT", 6379)),
    db=int(os.getenv("REDIS_DB", 0))
)

# number of workers to jobs in below queues
# validate_request_queue: 4
# openalex_queue: 8
# screening_queue: 24
# extraction_queue: 12

validate_request_queue = Queue("validate_jobs", connection=redis)
openalex_queue  = Queue("openalex_jobs", connection=redis)
screening_queue  = Queue("screening_jobs", connection=redis)
extraction_queue = Queue("extraction_jobs", connection=redis)
