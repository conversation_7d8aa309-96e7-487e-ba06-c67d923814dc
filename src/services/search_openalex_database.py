from dotenv import load_dotenv
load_dotenv()

# Add project root to Python path for imports
import sys
from pathlib import Path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import os
from typing import Union
import json, requests
import pycountry

from unstructured.cleaners.core import replace_unicode_quotes
from src.helpers.general_utils import setup_logging
from src.models.epidemiology_research import OpenAlexSearchRequest

LOG_LEVEL = os.getenv("LOG_LEVEL", "WARNING")
logger = setup_logging(logging_level=LOG_LEVEL)

class OpenAlexClient:
    def __init__(self, apply_topic_filtering:bool=False):
        self.apply_topic_filtering = apply_topic_filtering

        # Create epidemiology topic names set for O(1) lookup 
        with open(os.path.join(project_root, "data", "openalex_interest_topics.json")) as f:
            self.topics = {
                topic.get("display_name") for topic in json.load(f)
                if topic.get("display_name", None) is not None
            }

        # load constant search query terms
        with open(os.path.join(project_root, "data", "openalex_query_terms.json")) as f:
            self.search_query_terms = json.load(f)
        self.constant_search_query_terms = ""
        for key, terms in self.search_query_terms.items():
            if key.startswith("AND_") and self.constant_search_query_terms=="":
                self.constant_search_query_terms = "(\"" + "\" OR \"".join(terms) + "\")"
            elif key.startswith("AND_") and self.constant_search_query_terms!="":
                self.constant_search_query_terms += " AND (\"" + "\" OR \"".join(terms) + "\")"
            elif key == "NOT":
                self.constant_search_query_terms += " NOT (\"" + "\" OR \"".join(terms) + "\")"
            else:
                raise ValueError(f"Invalid key: {key}")

    def _process_identifiers(
        self,
        work: dict
    ) -> dict:
        """
        Process the identifiers of a work
        """
        identifiers = {}
        if work.get("ids", {}).get("pmid", None) is not None:
            identifiers["pmid"] = work.get("ids", {}).get("pmid")
        if work.get("ids", {}).get("pmcid", None) is not None:
            identifiers["pmcid"] = work.get("ids", {}).get("pmcid")
        return identifiers
    
    def _publication_access_information(
        self,
        work: dict
    ) -> dict:
        """
        Parse the publication access information from the work object
        """
        access_information = {}
        if work.get("primary_location", {}).get("pdf_url", None) is not None:
            access_information["pdf_url"] = work.get("primary_location", {}).get("pdf_url")
        if work.get("primary_location", {}).get("type", None) is not None:
            access_information["publication_type"] = work.get("primary_location", {}).get("type")
        if work.get("publication_year", None) is not None:
            access_information["publication_year"] = int(work.get("publication_year", -1))
        return access_information

    def _construct_abstract(
        self,
        work: dict
    ) -> Union[str, None]:
        """
        Construct the publication abstract of a work using abstract_inverted_index 
        """
        if len(work.get("abstract_inverted_index", {})) > 3:
            raw_abstract = work.get("abstract_inverted_index", {})
        else:
            return None
        
        # Create a list to store (position, word) tuples
        position_word_pairs = []
        for word, positions in raw_abstract.items():
            for position in positions:
                position_word_pairs.append((position, word))
        
        # Sort by position (first element of tuple) and extract words
        position_word_pairs.sort(key=lambda x: x[0])
        abstract = " ".join(word for _, word in position_word_pairs)
        abstract = replace_unicode_quotes(abstract)
        return abstract
    
    def _parse_institute_countries(
        self,
        work: dict
    ) -> list:
        """
        Parse the countries of the institutes from work object
        """
        # Use set from the beginning instead of converting list to set later
        country_codes = set()
        for authorship in work.get("authorships", []):
            if authorship.get("institutions", []):
                for institution in authorship.get("institutions", []):
                    if institution.get("country_code", None) is not None:
                        country_codes.add(institution.get("country_code"))
        return list(country_codes)
    
    def construct_search_query(
        self,
        disease_keywords: list,
        country_keywords: list,
        year_keywords: list,
    ) -> str:
        """
        Construct the diease epidemiology study search query for the OpenAlex API
        """

        query = "(\"" + "\" OR \"".join(disease_keywords) + "\") AND " + \
                "(\"" + "\" OR \"".join(country_keywords) + "\") AND " + \
                "(\"" + "\" OR \"".join(year_keywords) + "\") AND " + \
                self.constant_search_query_terms
        return query
    
    def _filter_works(
        self,
        works: list
    ) -> list:
        """
        Filter and process the works based on the interested topics, and abstract availability. Each work contains keys

        Args:
            works (list): A list of works.

        Returns:
            filtered_works (list): A list of filtered works.
        """
        filtered_works = []
        for work in works:
            abstract = self._construct_abstract(work)
            if abstract is None:
                continue

            if self.apply_topic_filtering:
                if not any( topic['display_name'] in self.topics for topic in work.get("topics", []) ):
                    continue

            modified_work = self._process_identifiers(work)
            modified_work.update(self._publication_access_information(work))
            modified_work["title"] = str(work.get("title", ""))
            modified_work["abstract"] = str(abstract)
            modified_work["institute_countries"] = self._parse_institute_countries(work)
            filtered_works.append(modified_work)
        return filtered_works

    def search_works(
        self,
        search_query: str,
        min_published_date: str="2000-01-01",
        max_published_date: str="2025-12-31",
        cursor: str="*"
    ) -> tuple[list, str]:
        """
        Search the work entities in OpenAlex database using search terms, published country and published time period

        Args:
            search_query (str): A openalex format query with boolean operators "OR", "AND" and "NOT" with phrase enclosed in double quotes.
            min_published_date (str): The minimum published date of the works in the format YYYY-MM-DD.
            max_published_date (str): The maximum published date of the works in the format YYYY-MM-DD.
            cursor (str): The cursor to start the search from.

        Returns:
            works (list): A list of works that match the criteria. Each work is a dictionary with the following keys:
                - pmid (str): The PMID of the work.
                - pmcid (str): The PMCID of the work.
                - publication_type (str): The type of the publication.
                - publication_year (int): The publication year of the work.
                - title (str): The title of the work.
                - abstract (str): The abstract of the work.
                - pdf_url : The URL of the PDF of the publication.
            next_cursor (str): The cursor to start the next search from.
        """
        base_url = "https://api.openalex.org/works"
        works = []

        # Combine keywords into a single search query using logical OR
        text_query = f"default.search:{search_query}"
        # country_code = f"institutions.country_code:{selected_country}"
        publication_period = f"from_publication_date:{min_published_date},to_publication_date:{max_published_date}"
        contains_abstract = "has_abstract:true"
        contains_pmcid = "has_pmcid:true"
        is_open_accessible = "has_oa_accepted_or_published_version:true"
        is_retracted = "is_retracted:false"
        language_identifier = "language:en"

        selected_fields = [
            "ids",
            "title",
            "publication_year",
            "abstract_inverted_index",
            "primary_location",
            "authorships",
            "topics"
        ]

        filter_query = ",".join([
            text_query,
            # country_code, 
            publication_period, 
            contains_abstract, 
            contains_pmcid, 
            is_open_accessible,
            is_retracted,
            language_identifier
        ])

        # initial API call in pagination
        select_query = ",".join(selected_fields)
        params = {
            "filter": filter_query,
            "select": select_query,
            "per_page": 200,
            "sort": "relevance_score:desc",
            "cursor": cursor
        }
        response = requests.get(base_url, params=params, timeout=30)
        response.raise_for_status()
        
        # Store parsed JSON to avoid redundant parsing
        response_data = response.json()
        retrieved_works = response_data.get("results", [])
        filtered_retrieved_works = self._filter_works(retrieved_works)
        works.extend(filtered_retrieved_works)

        total_number_of_works = response_data.get("meta", {}).get("count", 0)
        logger.debug(f"Total number of works: {total_number_of_works}\n API query: {params}")
        next_cursor = response_data.get("meta", {}).get("next_cursor", None)
        
        # Return the number of works found and the next cursor
        return works, next_cursor

def execute_openalex_search(search_request: OpenAlexSearchRequest) -> tuple[list, OpenAlexSearchRequest]:
    disease_keywords = search_request.disease_names

    country_object = pycountry.countries.get(name=search_request.selected_country)
    country_keywords = [search_request.selected_country]
    country_alpha_2 = getattr(country_object, "alpha_2") 
    if country_alpha_2 is None:
        raise ValueError(f"Country with name: {search_request.selected_country} not found in pycountry")
    country_keywords.append(country_alpha_2)

    country_official_name = getattr(country_object, "official_name", None)
    country_alpha_3 = getattr(country_object, "alpha_3", None)
    if country_official_name is not None:
        country_keywords.append(country_official_name)
    if country_alpha_3 is not None:
        country_keywords.append(country_alpha_3)
    
    min_year = int(search_request.min_published_date.split("-")[0])
    max_year = int(search_request.max_published_date.split("-")[0])
    year_keywords = [str(year) for year in range(min_year, max_year+1)]

    oaclient = OpenAlexClient()
    query = oaclient.construct_search_query(disease_keywords, country_keywords, year_keywords)
    logger.debug(f"OpenAlex Search Query: {query}")
    works, search_request.cursor = oaclient.search_works(
        search_query=query,
        min_published_date=search_request.min_published_date,
        max_published_date=search_request.max_published_date,
        cursor=search_request.cursor
    )
    return works, search_request

if __name__ == "__main__":
    logger.debug("Testing OpenAlex Search...")
    search_request = OpenAlexSearchRequest(
        disease_names=["Ulcerative Colitis"],
        selected_country="United States",
        min_published_date="2000-01-01",
        max_published_date="2025-12-31",
        topk_articles=10,
        cursor="*",
    )

    works, search_request = execute_openalex_search(search_request)
    logger.debug(f"Total number of works: {len(works)}, but limiting print top 5 works")
    logger.debug(f"Next pagination cursor: {search_request.cursor}")
    logger.debug(f"Data fields: {works[0].keys()}")
    for idx,work in enumerate(works[:5]):
        logger.debug(f"{idx+1}. {work['title']}")
        logger.debug(f"{work['abstract'][:50]}...")
        logger.debug("="*50+"\n")