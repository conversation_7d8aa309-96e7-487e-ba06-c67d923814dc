from dotenv import load_dotenv
load_dotenv()

# get the project rootpath for other filepath creation
import sys
from pathlib import Path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from textwrap import dedent
from typing import List, Optional, Dict, Any
from agno.agent import Agent
from agno.models.openai import OpenAIChat
import json
import os
from src.models.epidemiology_research import DiseaseValidation, OpenAlexSearchRequest, ResearchRequest
from src.helpers.config import DiseaseValidationConfig
import random
import yaml
import traceback
from agno.db.postgres import PostgresDb
import pycountry
from src.helpers.general_utils import setup_logging

LOG_LEVEL = os.getenv("LOG_LEVEL", "WARNING")
logger = setup_logging(logging_level=LOG_LEVEL)

# Global variables
config = DiseaseValidationConfig()

with open(os.path.join(f"{project_root}/src/prompts/validate_disease_name.yaml")) as f:
    prompts = yaml.safe_load(f)

def validate_disease_name(user_input_term: str) -> Dict[str, Any]:
    # Setup your database
    db = PostgresDb(
        db_url=os.getenv("POSTGRES_DATABASE_URL"),
        memory_table=os.getenv("DISEASE_NAME_TABLE"),
    )

    disease_name_validation_agent = Agent(
        model=OpenAIChat(id=config.model_id, temperature=config.temperature),
        description=prompts["description"],
        instructions=prompts["instructions"],
        reasoning=config.use_reasoning,
        db=db,
        enable_agentic_memory=True,
        enable_user_memories=True,
        add_memories_to_context=True,
        retries=config.api_retries,
        exponential_backoff=True,
        output_schema=DiseaseValidation,
        debug_mode=config.debug_agent,
        telemetry=False,
    )

    try:
        response = disease_name_validation_agent.run(
            f'Validate whether the following term: "{user_input_term}". Update your memory using `update_user_memory` tool to remember the standardized disease name if it is a valid disease name.',
            user_id='epiresearch_admin'
        )

        if isinstance(response.content, DiseaseValidation):
            output = response.content.model_dump()
        else:
            raise ValueError(f"Disease Name Validation - Pydantic Model Validation Error: {response}\n\n type:{type(response)}")
    except Exception as e:
        output = {
            "is_disease": False,
            "explanation": f"Error during disease name validation: {e}\n\n{traceback.format_exc()}",
            "suggested_corrections": None,
        }
    

    if config.debug_agent:
        print(f"Existing Disease Validation Agent Memories:")
        print(disease_name_validation_agent.get_user_memories(user_id='epiresearch_admin'))
    return output

def validate_research_request(research_request: ResearchRequest) -> Dict[str, Any]:
    validation_status = True

    # Use green tick for valid disease, red cross for invalid
    disease_validation_result = validate_disease_name(research_request.disease_name)
    standardized_disease_names = []
    markdown_response = ""
    if  disease_validation_result["is_disease"]:
        markdown_response += (
            f"✅ **'{research_request.disease_name}' is a valid disease term.**\n\n"
            f"**Explanation:** {disease_validation_result['explanation']}\n"
        )
        standardized_disease_names.extend(
            [disease_validation_result["standardized_disease_name"]] + \
            disease_validation_result["standard_disease_synonyms"]
        )
    else:
        validation_status = False
        markdown_response += (
            f"❌ **'{research_request.disease_name}' is NOT a valid disease term.**\n\n"
            f"**Explanation:** {disease_validation_result['explanation']}\n"
        )
        if disease_validation_result.get("suggested_corrections"):
            markdown_response += (
                "\n**Suggested Corrections:**\n"
                + "\n".join(
                    f"- {suggestion}"
                    for suggestion in disease_validation_result["suggested_corrections"]
                )
            )

    # check country name
    standardized_country_name = ""
    full_country_names = [getattr(country, "name") for country in pycountry.countries]
    if research_request.selected_country in full_country_names:
        markdown_response += (
            f"✅ **'{research_request.selected_country}' is a valid country term.**\n"
        )
        standardized_country_name = str(research_request.selected_country)
    else:
        validation_status = False
        markdown_response += (
            f"❌ **'{research_request.selected_country}' is NOT a valid country term.** Please enter a valid country name.\n"
        )
    
    # check min_published_date is before max_published_date
    formatted_min_published_date = ""
    formatted_max_published_date = ""
    if research_request.min_published_date > research_request.max_published_date:
        validation_status = False
        markdown_response += (
            f"❌ **'{research_request.min_published_date}' is before '{research_request.max_published_date}'.** Please enter a valid date range.\n"
        )
    else:
        formatted_min_published_date = str(research_request.min_published_date).replace(".", "-")
        formatted_max_published_date = str(research_request.max_published_date).replace(".", "-")
    
    # check topk_articles is non-negative
    topk_articles = 0
    if research_request.topk_articles <= 0:
        validation_status = False
        markdown_response += (
            f"❌ **'{research_request.topk_articles}' is not a valid number of articles.** Please enter a valid number of articles.\n"
        )
    else:
        topk_articles = research_request.topk_articles
    
    # create validated research request for downstream processing
    openalex_search_request = OpenAlexSearchRequest(
        disease_names=standardized_disease_names,
        selected_country=standardized_country_name,
        min_published_date=formatted_min_published_date,
        max_published_date=formatted_max_published_date,
        topk_articles=topk_articles,
        cursor="*",
    )

    return {
        "validation_status": validation_status,
        "validation_response": markdown_response,
        "openalex_search_request": openalex_search_request
    }


if __name__ == "__main__":
    print("Testing Research Request Validation...")
    research_request = ResearchRequest(
        disease_name="Ulcerative Colitis",
        selected_country="United States",
        min_published_date="1970.01.01",
        max_published_date="2025.01.01",
        topk_articles=10,
    )
    response = validate_research_request(research_request)
    print("Validation Status:")
    print(response["validation_status"])
    print("Validation Response:")
    print(response["validation_response"])
    print("OpenAlex Search Request:")
    print(response["openalex_search_request"])