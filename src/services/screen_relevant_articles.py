from dotenv import load_dotenv
load_dotenv()

# get the project rootpath for other filepath creation
import sys
from pathlib import Path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import random, traceback
from textwrap import dedent
from typing import Dict, List, Generator
from agno.agent import Agent
from agno.models.openai import OpenAIChat
import json
import os
from pydantic import BaseModel
from agno.tools.reasoning import ReasoningTools
from concurrent.futures import ThreadPoolExecutor, as_completed

from pydantic import BaseModel
import yaml
from pathlib import Path

from src.models.epidemiology_data import AbstractScreeningResult, DataExtractionInput
from src.helpers.config import AbstractScreeningConfig

from src.helpers.general_utils import setup_logging

LOG_LEVEL = os.getenv("LOG_LEVEL", "WARNING")
logger = setup_logging(logging_level=LOG_LEVEL)

# Config and prompts
project_root = Path(__file__).parent.parent.parent
with open(os.path.join(f"{project_root}/src/prompts/abstract_screening.yaml")) as f:
    prompts = yaml.safe_load(f)

config = AbstractScreeningConfig()


def screen_abstract_for_disease_epidemiology(
    pmc_link:str, disease: str, title: str, abstract: str
) -> DataExtractionInput:
    """
    Convenience function to screen a single title and abstract for a specific disease.

    Args:
        pmc_link (str): The Pubmed central website link to the article.
        disease (str): The disease name to check for in the title and abstract.
        title (str): The article title to analyze.
        abstract (str): The abstract text to analyze.

    Returns:
        Dict[str, bool]: Dictionary containing screening result fields of `AbstractScreeningResult`.
    """
    screening_agent = Agent(
        model=OpenAIChat(id=config.model_id, temperature=config.temperature),
        description=prompts["description"],
        instructions=prompts["instructions"],
        reasoning=config.use_reasoning,
        retries=config.api_retries,
        exponential_backoff=True,
        output_schema=AbstractScreeningResult,
        debug_mode=config.debug_agent,
        telemetry=False
    )
    
    prompt = f"""
    Analyze the following research article with provided data:

    **DISEASE NAME:** {disease}

    **TITLE:** {title}

    **ABSTRACT:**{abstract}
    """

    data_extraction_input = DataExtractionInput(
        pmc_link=pmc_link,
        disease_name=disease,
    )
    try:
        response = screening_agent.run(prompt, disease=disease)
        if isinstance(response.content, AbstractScreeningResult):
            result = response.content.model_dump()
            data_extraction_input.is_epidemiology_focus = result['is_epidemiology_focus']
            data_extraction_input.reports_prevalence = result['reports_prevalence']
            data_extraction_input.reports_incidence = result['reports_incidence']
            data_extraction_input.screening_status = "SUCCESS"
            data_extraction_input.screening_message = ""
        else:
            data_extraction_input.screening_status = "FAILED"
            data_extraction_input.screening_message = f"Failed to screen abstract for disease epidemiology: {traceback.format_exc()}"
    except Exception as e:
        data_extraction_input.screening_status = "FAILED"
        data_extraction_input.screening_message = f"Failed to screen abstract for disease epidemiology: {traceback.format_exc()}"
    return data_extraction_input


if __name__ == "__main__":
    logger.debug("Testing Disease-Specific Abstract Screening Agent...")

    # Test with a sample epidemiology abstract for Type 2 diabetes

    pmc_link = "https://www.ncbi.nlm.nih.gov/pmc/articles/3576554"
    disease_name = "Ulcerative Colitis"
    article_title = "Recent Trends in the Prevalence of Crohn's Disease and Ulcerative Colitis in a Commercially Insured US Population"
    article_abstract = """Purpose: Most US inflammatory bowel disease (IBD) epidemiology studies conducted to date have sampled small, geographically restricted populations and have not examined time trends. The aim of our study was to determine the prevalence of Crohn's disease (CD) and ulcerative colitis (UC) in a commercially insured US population and compare prevalences across sociodemographic characteristics and time.
    Methods: Using claims data from approximately 12 million Americans, we performed three consecutive 2-year cross-sectional studies. Cases of CD and UC were identified using a previously described algorithm. Prevalence was estimated by dividing cases by individuals in the source population. Logistic regression was used to compare prevalences by region, age, and sex.
    Results: In 2009, the prevalences of CD and UC in children were 58 [95% confidence interval (CI) 55-60] and 34 (95 % CI 32-36), respectively. In adults, the respective prevalences were 241 (95 % CI 238-245) and 263 (95 % CI 260-266). Data analysis revealed that IBD prevalences have slightly increased over time. Based on census data, an estimated 1,171,000 Americans have IBD (565,000 CD and 593,000 UC).
    Conclusions: Analysis of the epidemiological data revealed an increasing burden of IBD in recent years, which may be used to inform policy.
    """

    result = screen_abstract_for_disease_epidemiology(
        pmc_link, 
        disease_name, 
        article_title, 
        article_abstract
    )
    logger.debug("Test Response of Screening Result:")
    logger.debug(result.model_dump_json(indent=4))