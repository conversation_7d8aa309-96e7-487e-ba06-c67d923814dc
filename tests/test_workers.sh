#!/usr/bin/env bash

set -euo pipefail

ROOT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")"/.. && pwd)"
cd "$ROOT_DIR"

echo "[test] Starting minimal set of workers (1 per queue)"

# Keep the test lightweight: 1 worker per queue
export VALIDATE_WORKERS=1
export OPENALEX_WORKERS=1
export SCREENING_WORKERS=1
export EXTRACTION_WORKERS=1

# Start workers in background (script itself waits on children, so run in subshell)
(
  ./scripts/start_workers.sh
) &
START_PID=$!

# Give workers time to spawn
sleep 4

echo "[test] Verifying workers are running"
for q in validate_jobs openalex_jobs screening_jobs extraction_jobs; do
  if ! pgrep -f "rq worker .*--name ${q}-" >/dev/null; then
    echo "[test][FAIL] Expected worker for queue '$q' was not found"
    kill "$START_PID" || true
    exit 1
  else
    echo "[test][OK] Found worker process for '$q'"
  fi
done

echo "[test] Stopping workers"
./scripts/stop_workers.sh

# Allow graceful shutdown
sleep 2

echo "[test] Verifying no workers remain"
LEFTOVER=false
for q in validate_jobs openalex_jobs screening_jobs extraction_jobs; do
  if pgrep -f "rq worker .*--name ${q}-" >/dev/null; then
    echo "[test][FAIL] Worker for '$q' still running"
    LEFTOVER=true
  else
    echo "[test][OK] No worker for '$q'"
  fi
done

if [[ "$LEFTOVER" == true ]]; then
  exit 1
fi

echo "[test][SUCCESS] Start/stop worker scripts function correctly."


