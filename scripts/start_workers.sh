#!/usr/bin/env bash

set -euo pipefail

# Change to project root (one level up from this script's directory)
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
cd "$PROJECT_ROOT"

# Export variables from .env if present (without requiring envsubst/xargs)
set -a
if [[ -f .env ]]; then
  # shellcheck disable=SC1091
  . ./.env
fi
set +a

# Redis connection parameters with defaults matching src/services/redis_queues.py
REDIS_HOST="${REDIS_HOST:-localhost}"
REDIS_PORT="${REDIS_PORT:-6379}"
REDIS_DB="${REDIS_DB:-0}"
REDIS_URL="redis://${REDIS_HOST}:${REDIS_PORT}/${REDIS_DB}"

# Worker counts (override via env if desired)
VALIDATE_WORKERS="${VALIDATE_WORKERS:-4}"
OPENALEX_WORKERS="${OPENALEX_WORKERS:-8}"
SCREENING_WORKERS="${SCREENING_WORKERS:-24}"
EXTRACTION_WORKERS="${EXTRACTION_WORKERS:-12}"

# Queues as defined in src/services/redis_queues.py
VALIDATE_QUEUE="validate_jobs"
OPENALEX_QUEUE="openalex_jobs"
SCREENING_QUEUE="screening_jobs"
EXTRACTION_QUEUE="extraction_jobs"

LOG_DIR="$PROJECT_ROOT/logs/rq"
mkdir -p "$LOG_DIR"

PIDS=()

start_workers_for_queue() {
  local queue_name="$1"
  local count="$2"

  if [[ "$count" -le 0 ]]; then
    echo "Skipping queue '$queue_name' (count=$count)"
    return 0
  fi

  echo "Starting $count worker(s) for queue '$queue_name' using $REDIS_URL"
  for i in $(seq 1 "$count"); do
    # Name each worker for easier identification
    local worker_name="${queue_name}-${i}"
    local log_file="$LOG_DIR/${queue_name}-${i}.log"

    # Use uv to run within the project's virtual environment
    # --with-scheduler lets one worker per process also run the scheduler loop
    # We only enable scheduler on the first worker of each queue to avoid duplicates
    if [[ "$i" -eq 1 ]]; then
      nohup uv run rq worker --url "$REDIS_URL" "$queue_name" \
        --name "$worker_name" --with-scheduler \
        >>"$log_file" 2>&1 &
    else
      nohup uv run rq worker --url "$REDIS_URL" "$queue_name" \
        --name "$worker_name" \
        >>"$log_file" 2>&1 &
    fi

    PIDS+=("$!")
    echo "  - started $worker_name (pid $!) -> $log_file"
  done
}

graceful_shutdown() {
  echo "\nStopping ${#PIDS[@]} worker process(es)..."
  for pid in "${PIDS[@]}"; do
    if kill -0 "$pid" >/dev/null 2>&1; then
      kill "$pid" || true
    fi
  done

  # Wait a moment, then force kill any stragglers
  sleep 2
  for pid in "${PIDS[@]}"; do
    if kill -0 "$pid" >/dev/null 2>&1; then
      kill -9 "$pid" || true
    fi
  done
  echo "All workers stopped."
}

trap graceful_shutdown INT TERM EXIT

# Start workers per queue
start_workers_for_queue "$VALIDATE_QUEUE" "$VALIDATE_WORKERS"
start_workers_for_queue "$OPENALEX_QUEUE" "$OPENALEX_WORKERS"
start_workers_for_queue "$SCREENING_QUEUE" "$SCREENING_WORKERS"
start_workers_for_queue "$EXTRACTION_QUEUE" "$EXTRACTION_WORKERS"

echo "\nWorkers are running. Tail logs with:"
echo "  tail -f $LOG_DIR/*.log"

# Keep script in foreground so trap can handle signals; wait on children
wait


