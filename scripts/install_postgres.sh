#!/usr/bin/env bash

set -euo pipefail
IFS=$'\n\t'

require_root() {
	if [[ "${EUID}" -ne 0 ]]; then
		echo "This script must be run as root (try: sudo $0)" >&2
		exit 1
	fi
}

detect_pkg_mgr() {
	if command -v apt-get >/dev/null 2>&1; then
		echo apt
		return 0
	fi
	echo "Unsupported distribution. Only Debian/Ubuntu (apt) are supported." >&2
	exit 1
}

install_postgres_apt() {
	echo "Updating apt package index..."
	apt-get update -y

	echo "Installing PostgreSQL server and contrib packages..."
	DEBIAN_FRONTEND=noninteractive apt-get install -y postgresql postgresql-contrib
}

enable_and_start_service() {
    local service_name="postgresql"

    echo "Attempting to manage PostgreSQL via systemd..."
    if command -v systemctl >/dev/null 2>&1; then
        if systemctl list-unit-files | grep -q "^${service_name}\\.service"; then
            echo "Enabling ${service_name} to start on boot..."
            systemctl enable "${service_name}" || true

            echo "Starting ${service_name} service..."
            systemctl restart "${service_name}" || true
        else
            echo "${service_name}.service not listed. Will try alternative methods." >&2
        fi
    else
        echo "systemctl not available. Will try alternative methods." >&2
    fi

    echo "If systemd control was insufficient, trying SysV init service..."
    if command -v service >/dev/null 2>&1; then
        service postgresql status >/dev/null 2>&1 || service postgresql start || true
    fi

    echo "Checking for Debian cluster management (pg_ctlcluster)..."
    if command -v pg_lsclusters >/dev/null 2>&1 && command -v pg_ctlcluster >/dev/null 2>&1; then
        # Ensure at least one cluster is online
        if ! pg_lsclusters | awk 'NR>1 {print $4}' | grep -q "online"; then
            # Start all clusters
            while read -r ver name _; do
                [ -n "$ver" ] || continue
                echo "Starting cluster ${ver}/${name}..."
                pg_ctlcluster "$ver" "$name" start || true
                systemctl enable "postgresql@${ver}-$(echo "$name")" 2>/dev/null || true
            done < <(pg_lsclusters | awk 'NR>1 {print $1, $2, $4}')
        fi
    fi

    echo "Checking PostgreSQL readiness..."
    if command -v pg_isready >/dev/null 2>&1; then
        pg_isready || {
            echo "PostgreSQL is not ready yet. Waiting for 5 seconds and retrying..."
            sleep 5
            pg_isready || {
                echo "PostgreSQL did not become ready in time." >&2
                if command -v systemctl >/dev/null 2>&1; then
                    systemctl status postgresql | cat || true
                fi
                exit 1
            }
        }
    else
        echo "pg_isready not found; proceeding assuming the service is up."
    fi
}

main() {
	require_root

	case "$(detect_pkg_mgr)" in
		apt)
			install_postgres_apt
			;;
		*)
			echo "Unexpected package manager." >&2
			exit 1
			;;
	esac

	enable_and_start_service

	echo "PostgreSQL installation and service setup complete."
}

main "$@"


