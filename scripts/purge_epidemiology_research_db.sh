#!/usr/bin/env bash

set -euo pipefail
IFS=$'\n\t'

DB_NAME=${DB_NAME:-"epidemiology_research"}
DB_USER=${DB_USER:-"epiresearch_admin"}
PSQL_BIN=${PSQL_BIN:-"psql"}
DB_HOST=${DB_HOST:-"localhost"}
DB_PORT=${DB_PORT:-"5432"}

usage() {
	echo "Usage: DB_NAME=<name> DB_USER=<user> $0" >&2
	echo "This script will COMPLETELY DELETE the epidemiology research database and user." >&2
	echo "This action is IRREVERSIBLE!" >&2
}

ensure_psql_ready() {
	if command -v pg_isready >/dev/null 2>&1; then
		pg_isready -h "${DB_HOST}" -p "${DB_PORT}" || {
			echo "PostgreSQL is not ready. Waiting 5 seconds..."
			sleep 5
			pg_isready -h "${DB_HOST}" -p "${DB_PORT}" || {
				echo "PostgreSQL did not become ready in time." >&2
				exit 1
			}
		}
	else
		echo "pg_isready not found; proceeding."
	fi
}

run_as_postgres() {
	sudo -u postgres bash -c "$*"
}

user_exists() {
	run_as_postgres "${PSQL_BIN} -tAc \"SELECT 1 FROM pg_roles WHERE rolname='${DB_USER}'\"" | grep -q 1
}

db_exists() {
	run_as_postgres "${PSQL_BIN} -tAc \"SELECT 1 FROM pg_database WHERE datname='${DB_NAME}'\"" | grep -q 1
}

confirm_deletion() {
	echo ""
	echo "⚠️  WARNING: This will COMPLETELY DELETE the following:"
	echo "   - Database: '${DB_NAME}'"
	echo "   - User/Role: '${DB_USER}'"
	echo "   - ALL DATA in the database"
	echo ""
	echo "This action is IRREVERSIBLE!"
	echo ""
	
	while true; do
		read -p "Type 'YES' to confirm deletion (case sensitive): " confirmation
		if [[ "$confirmation" == "YES" ]]; then
			echo "Confirmation received. Proceeding with deletion..."
			break
		elif [[ "$confirmation" == "" ]]; then
			echo "No input provided. Exiting without deletion."
			exit 0
		else
			echo "Invalid confirmation. Please type 'YES' exactly (case sensitive) or press Enter to cancel."
		fi
	done
}

drop_database() {
	if db_exists; then
		echo "Dropping database '${DB_NAME}'..."
		run_as_postgres "${PSQL_BIN} -v ON_ERROR_STOP=1 -c \"DROP DATABASE IF EXISTS \\\"${DB_NAME}\\\";\""
		echo "Database '${DB_NAME}' has been dropped."
	else
		echo "Database '${DB_NAME}' does not exist."
	fi
}

drop_user() {
	if user_exists; then
		echo "Dropping user '${DB_USER}'..."
		run_as_postgres "${PSQL_BIN} -v ON_ERROR_STOP=1 -c \"DROP ROLE IF EXISTS \\\"${DB_USER}\\\";\""
		echo "User '${DB_USER}' has been dropped."
	else
		echo "User '${DB_USER}' does not exist."
	fi
}

print_summary() {
	echo ""
	echo "✅ Purge completed successfully!"
	echo "   - Database '${DB_NAME}' has been deleted"
	echo "   - User '${DB_USER}' has been deleted"
	echo ""
	echo "The epidemiology research database has been completely removed."
}

main() {
	echo "🗑️  Epidemiology Research Database Purge Script"
	echo "=============================================="
	
	ensure_psql_ready
	confirm_deletion
	drop_database
	drop_user
	print_summary
}

main "$@"
