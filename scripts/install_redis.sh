#!/usr/bin/env bash

set -euo pipefail
IFS=$'\n\t'

require_root() {
	if [[ "${EUID}" -ne 0 ]]; then
		echo "This script must be run as root (try: sudo $0)" >&2
		exit 1
	fi
}

detect_pkg_mgr() {
	if command -v apt-get >/dev/null 2>&1; then
		echo apt
		return 0
	fi
	echo "Unsupported distribution. Only Debian/Ubuntu (apt) are supported." >&2
	exit 1
}

install_redis_apt() {
	echo "Updating apt package index..."
	apt-get update -y

	echo "Installing Redis server..."
	DEBIAN_FRONTEND=noninteractive apt-get install -y redis-server
}

enable_and_start_service() {
	local service_name="redis-server"

	echo "Attempting to manage Redis via systemd..."
	if command -v systemctl >/dev/null 2>&1; then
		if systemctl list-unit-files | grep -q "^${service_name}\\.service"; then
			echo "Enabling ${service_name} to start on boot..."
			systemctl enable "${service_name}" || true

			echo "Starting ${service_name} service..."
			systemctl restart "${service_name}" || true
		else
			echo "${service_name}.service not listed. Will try alternative methods." >&2
		fi
	else
		echo "systemctl not available. Will try alternative methods." >&2
	fi

	echo "If systemd control was insufficient, trying SysV init service..."
	if command -v service >/dev/null 2>&1; then
		service redis-server status >/dev/null 2>&1 || service redis-server start || true
	fi

	echo "Checking Redis readiness..."
	if command -v redis-cli >/dev/null 2>&1; then
		if ! redis-cli -h 127.0.0.1 -p 6379 ping | grep -q "PONG"; then
			echo "Redis is not ready yet. Waiting for 5 seconds and retrying..."
			sleep 5
			if ! redis-cli -h 127.0.0.1 -p 6379 ping | grep -q "PONG"; then
				echo "Redis did not become ready in time." >&2
				if command -v systemctl >/dev/null 2>&1; then
					systemctl status redis-server | cat || true
					journalctl -u redis-server -n 200 --no-pager | cat || true
				fi
				exit 1
			fi
		fi
	else
		echo "redis-cli not found; proceeding assuming the service is up."
	fi
}

main() {
	require_root

	case "$(detect_pkg_mgr)" in
		apt)
			install_redis_apt
			;;
		*)
			echo "Unexpected package manager." >&2
			exit 1
			;;
		esac

	enable_and_start_service

	echo "Redis installation and service setup complete."
}

main "$@"


