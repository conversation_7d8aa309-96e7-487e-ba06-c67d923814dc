#!/usr/bin/env bash

set -euo pipefail
IFS=$'\n\t'

DB_NAME=${DB_NAME:-"epidemiology_research"}
DB_USER=${DB_USER:-"epiresearch_admin"}
DB_PASSWORD=${DB_PASSWORD:-"epiresearch1234!"}
PSQL_BIN=${PSQL_BIN:-"psql"}
CREATEUSER_BIN=${CREATEUSER_BIN:-"createuser"}
CREATEDB_BIN=${CREATEDB_BIN:-"createdb"}
DB_HOST=${DB_HOST:-"localhost"}
DB_PORT=${DB_PORT:-"5432"}

usage() {
	echo "Usage: DB_NAME=<name> DB_USER=<user> DB_PASSWORD=<password> $0" >&2
}

ensure_psql_ready() {
	if command -v pg_isready >/dev/null 2>&1; then
		pg_isready -h "${DB_HOST}" -p "${DB_PORT}" || {
			echo "PostgreSQL is not ready. Waiting 5 seconds..."
			sleep 5
			pg_isready -h "${DB_HOST}" -p "${DB_PORT}" || {
				echo "PostgreSQL did not become ready in time." >&2
				exit 1
			}
		}
	else
		echo "pg_isready not found; proceeding."
	fi
}

run_as_postgres() {
	sudo -u postgres bash -c "$*"
}

user_exists() {
	run_as_postgres "${PSQL_BIN} -tAc \"SELECT 1 FROM pg_roles WHERE rolname='${DB_USER}'\"" | grep -q 1
}

db_exists() {
	run_as_postgres "${PSQL_BIN} -tAc \"SELECT 1 FROM pg_database WHERE datname='${DB_NAME}'\"" | grep -q 1
}

create_user_if_needed() {
	if user_exists; then
		echo "Role '${DB_USER}' already exists. Ensuring password is set."
		run_as_postgres "${PSQL_BIN} -v ON_ERROR_STOP=1 -c \"ALTER ROLE \\\"${DB_USER}\\\" WITH LOGIN PASSWORD '${DB_PASSWORD}';\""
	else
		echo "Creating role '${DB_USER}'..."
		run_as_postgres "${CREATEUSER_BIN} --login --no-createdb --no-createrole --no-superuser '${DB_USER}'"
		run_as_postgres "${PSQL_BIN} -v ON_ERROR_STOP=1 -c \"ALTER ROLE \\\"${DB_USER}\\\" WITH PASSWORD '${DB_PASSWORD}';\""
	fi
}

create_db_if_needed() {
	if db_exists; then
		echo "Database '${DB_NAME}' already exists."
	else
		echo "Creating database '${DB_NAME}' owned by '${DB_USER}'..."
		run_as_postgres "${CREATEDB_BIN} --owner='${DB_USER}' '${DB_NAME}'"
	fi
}

grant_privileges() {
	echo "Granting privileges on database '${DB_NAME}' to '${DB_USER}'..."
	run_as_postgres "${PSQL_BIN} -v ON_ERROR_STOP=1 -d '${DB_NAME}' -c \"GRANT ALL PRIVILEGES ON DATABASE \\\"${DB_NAME}\\\" TO \\\"${DB_USER}\\\";\""
	run_as_postgres "${PSQL_BIN} -v ON_ERROR_STOP=1 -d '${DB_NAME}' -c \"ALTER DATABASE \\\"${DB_NAME}\\\" OWNER TO \\\"${DB_USER}\\\";\""
}

print_database_url() {
	echo ""
	echo "PostgreSQL database URL for clients:"
	echo "postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}"
	echo ""
}

main() {
	if [[ -z "${DB_PASSWORD}" ]]; then
		echo "DB_PASSWORD must not be empty." >&2
		exit 1
	fi

	ensure_psql_ready
	create_user_if_needed
	create_db_if_needed
	grant_privileges

	echo "Database '${DB_NAME}' and role '${DB_USER}' are configured."
	print_database_url
}

main "$@"
