#!/usr/bin/env bash

set -euo pipefail

# Change to project root (one level up from this script's directory)
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
cd "$PROJECT_ROOT"

# Known queues as defined in src/services/redis_queues.py
ALL_QUEUES=(
  "validate_jobs"
  "openalex_jobs"
  "screening_jobs"
  "extraction_jobs"
)

usage() {
  echo "Usage: $0 [queue1 queue2 ...]" >&2
  echo "Without arguments, stops all queues: ${ALL_QUEUES[*]}" >&2
}

if [[ "${1-}" == "-h" || "${1-}" == "--help" ]]; then
  usage
  exit 0
fi

TARGET_QUEUES=()
if [[ $# -gt 0 ]]; then
  # Validate provided queues
  for q in "$@"; do
    found=false
    for known in "${ALL_QUEUES[@]}"; do
      if [[ "$q" == "$known" ]]; then
        found=true
        break
      fi
    done
    if [[ "$found" == false ]]; then
      echo "Unknown queue: $q" >&2
      echo "Known queues: ${ALL_QUEUES[*]}" >&2
      exit 1
    fi
    TARGET_QUEUES+=("$q")
  done
else
  TARGET_QUEUES=("${ALL_QUEUES[@]}")
fi

stop_queue() {
  local queue_name="$1"
  echo "Stopping workers for queue '$queue_name'..."

  # Match workers started with: rq worker --name ${queue_name}-N
  mapfile -t pids < <(pgrep -f "rq worker .*--name ${queue_name}-") || true

  if [[ ${#pids[@]} -eq 0 ]]; then
    echo "  No running workers found for $queue_name"
    return 0
  fi

  echo "  Sending SIGTERM to: ${pids[*]}"
  for pid in "${pids[@]}"; do
    if kill -0 "$pid" >/dev/null 2>&1; then
      kill "$pid" || true
    fi
  done

  # Wait briefly for graceful shutdown
  sleep 2

  # Force kill remaining
  mapfile -t remaining < <(printf '%s\n' "${pids[@]}" | xargs -r -n1 sh -c 'kill -0 "$0" 2>/dev/null && echo "$0"' || true)
  if [[ ${#remaining[@]} -gt 0 ]]; then
    echo "  Forcing SIGKILL to: ${remaining[*]}"
    for pid in "${remaining[@]}"; do
      kill -9 "$pid" || true
    done
  fi
  echo "  Queue '$queue_name' workers stopped."
}

for q in "${TARGET_QUEUES[@]}"; do
  stop_queue "$q"
done

echo "Done."


